"use client"

import {useState, useEffect} from "react"
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "lucide-react"

import {Avatar, Box, Dialog, Flex, Heading, IconButton, Text} from "@radix-ui/themes"
import ParticipantsSidebar from "@/src/client/PokerPlanning/infrastructure/nextjs/components/ParticipantsSidebar/ParticipantsSidebar"
import TicketsSidebar from "@/src/client/PokerPlanning/infrastructure/nextjs/components/TicketsSidebar/TicketsSidebar"
import VotingSection from "@/src/client/PokerPlanning/infrastructure/nextjs/components/VotingSection/VotingSection"
import {useAppDispatch, useAppSelector} from "@/src/client/Shared/store/appStore/hooks"
import {loadPokerPlanningSession} from "@/src/client/PokerPlanning/application/loadPokerPlanningSession/loadPokerPlanningSession"

const votingParticipants = [
  {id: 1, name: "Hideo V", avatar: "https://i.pravatar.cc/40?img=9"},
  {id: 2, name: "<PERSON><PERSON><PERSON>", avatar: "https://i.pravatar.cc/40?img=5"},
  {id: 3, name: "Lei Mag", avatar: "https://i.pravatar.cc/40?img=1"},
  {id: 4, name: "Yun Æt", avatar: "https://i.pravatar.cc/40?img=3"},
  {id: 5, name: "Joseph", avatar: "https://i.pravatar.cc/40?img=7"},
]

const votingParticipants2 = [
  {id: 6, name: "Nikolay", avatar: "https://i.pravatar.cc/40?img=8"},
  {id: 7, name: "Artur H", avatar: "https://i.pravatar.cc/40?img=4"},
  {id: 8, name: "Mohan", avatar: "https://i.pravatar.cc/40?img=2"},
  {id: 9, name: "Li Stef", avatar: "https://i.pravatar.cc/40?img=6"},
]




export default function PokerPlanningPage({sessionId}: {sessionId: string}) {
  const dispatch = useAppDispatch()
  const participants = useAppSelector(state => state.pokerPlanningSession.participants)
  const tickets = useAppSelector(state => state.pokerPlanningSession.tickets)

  const [selectedCard, setSelectedCard] = useState<number | string | null>(null)
  const [votesRevealed, setVotesRevealed] = useState(false)
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(false)
  const [rightSidebarOpen, setRightSidebarOpen] = useState(false)

  useEffect(() => {
    dispatch(loadPokerPlanningSession(sessionId))
  }, [dispatch, sessionId])

  return (
    <Box style={{height: '100vh', overflow: 'hidden', backgroundColor: '#e2e8fd'}}>
      {/* Header */}
      <Box
        style={{
          borderBottom: '1px solid var(--indigo-6)',
          position: 'sticky',
          top: 0,
          zIndex: 40,
        }}
        px="4"
        py="2"
      >
        <Flex justify="between" align="center" gap={{initial: "2", sm: "3"}}>
          <Flex align="center" gap={{initial: "2", sm: "3"}}>
            {/* Mobile menu button for participants */}
            <Dialog.Root open={leftSidebarOpen} onOpenChange={setLeftSidebarOpen}>
              <Dialog.Trigger>
                <Box display={{initial: "block", lg: "none"}}>
                  <IconButton variant="ghost">
                    <Menu size={16}/>
                  </IconButton>
                </Box>
              </Dialog.Trigger>
              <Dialog.Content style={{width: '90vw', maxWidth: '256px', padding: '16px'}}>
                <Dialog.Title>Participants</Dialog.Title>
                <ParticipantsSidebar participants={participants}/>
              </Dialog.Content>
            </Dialog.Root>

            <Heading size={{initial: "2", sm: "3", md: "4"}}>Poker Planning</Heading>
          </Flex>

          <Flex align="center" gap={{initial: "2", sm: "3"}}>
            <Flex align="center" gap="2" display={{initial: "none", sm: "flex"}}>
              <Text size="2" weight="medium">Matt</Text>
              <Avatar size="2" fallback="M" src="https://i.pravatar.cc/40?img=11"/>
            </Flex>

            <Box display={{initial: "none", sm: "block"}}>
              <IconButton variant="ghost">
                <Settings size={16}/>
              </IconButton>
            </Box>

            {/* Mobile menu button for tickets */}
            <Dialog.Root open={rightSidebarOpen} onOpenChange={setRightSidebarOpen}>
              <Dialog.Trigger>
                <Box display={{initial: "block", xl: "none"}}>
                  <IconButton variant="ghost">
                    <MoreHorizontal size={16}/>
                  </IconButton>
                </Box>
              </Dialog.Trigger>
              <Dialog.Content style={{width: '90vw', maxWidth: '320px', padding: '16px'}}>
                <Dialog.Title>Tickets</Dialog.Title>
                <TicketsSidebar tickets={tickets}/>
              </Dialog.Content>
            </Dialog.Root>
          </Flex>
        </Flex>
      </Box>

      <Flex style={{height: 'calc(100vh - 73px)', overflow: 'hidden'}}>
        {/* Left Sidebar - Desktop */}
        <Box
          className="backdrop-blur"
          width={{lg: "240px", xl: "256px"}}
          style={{
            borderRight: '1px solid var(--indigo-6)',
            height: '100%',
            overflow: 'hidden'
          }}
          p={{lg: "3", xl: "4"}}
          display={{initial: "none", lg: "block"}}
        >
          <ParticipantsSidebar participants={participants}/>
        </Box>

        {/* Main Content */}
        <Box
          style={{
            flex: 1,
            minWidth: 0,
            height: '100%',
            overflow: 'auto',
          }}
          p={{initial: "4"}}
        >
          {/* Current Ticket */}
          <Box mb={{initial: "6", md: "8"}}>
            <Flex direction="column" align="center" gap="2" mb={{initial: "3", md: "4"}}>
              <Text
                size="4"
                weight="bold"
                align="center"
                style={{maxWidth: '100%', wordBreak: 'break-word'}}
                color="gray"
              >
                Task: Use Kollabe for our next Poker Planning session
              </Text>
            </Flex>

            <VotingSection
              participantsRows={[votingParticipants, votingParticipants2]}
              selectedCard={selectedCard}
              onSelect={(card) =>
                card === 'reveal' ? setVotesRevealed(!votesRevealed) : setSelectedCard(card)
              }
            />
          </Box>
        </Box>

        <Box
          className="backdrop-blur"
          width={{xl: "320px"}}
          style={{
            borderLeft: '1px solid var(--indigo-6)',
            height: '100%',
            overflow: 'hidden'
          }}
          p={{xl: "4"}}
          display={{initial: "none", xl: "block"}}
        >
          <TicketsSidebar tickets={tickets}/>
        </Box>
      </Flex>
    </Box>
  )
}
