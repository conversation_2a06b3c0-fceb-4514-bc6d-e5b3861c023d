import {createAsyncThunk} from '@reduxjs/toolkit';
import {PokerPlanningSessionRepository} from '../ports/PokerPlanningSessionRepository';
import {
  pokerPlanningSessionLoaded,
  pokerPlanningSessionLoadingFailed,
  pokerPlanningSessionLoadingStarted,
} from '../../domain/PokerPlanningSession/pokerPlanningSessionSlice';

export const loadPokerPlanningSession = createAsyncThunk<void, string, {extra: {pokerPlanningSessionRepository: PokerPlanningSessionRepository}}>(
    'pokerPlanning/loadSession',
    async (sessionId, {dispatch, extra: {pokerPlanningSessionRepository}}) => {
        dispatch(pokerPlanningSessionLoadingStarted());
        const session = await pokerPlanningSessionRepository.findById(sessionId);
        if (!session) {
            dispatch(pokerPlanningSessionLoadingFailed());
            return;
        }
        dispatch(pokerPlanningSessionLoaded(session));
    }
);
