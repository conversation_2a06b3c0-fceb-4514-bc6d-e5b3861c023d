import { describe, it, expect } from 'vitest';
import schema from '@/convex/schema';
import { convexTest } from 'convex-test';
import { ConvexPokerPlanningSessionRepository } from '@/src/client/PokerPlanning/infrastructure/repositories/PokerPlanningSession/ConvexPokerPlanningSessionRepository';
import { ConvexReactClient } from 'convex/react';
import { createFakePokerPlanningSession } from '@/src/server/PokerPlanning/specs/helpers/createFakePokerPlanningSession';

function createClient(t: ReturnType<typeof convexTest>) {
  return { query: t.query } as unknown as ConvexReactClient;
}

describe('When using ConvexPokerPlanningSessionRepository', () => {
  it('should return a session when found', async () => {
    // Arrange
    const session = createFakePokerPlanningSession();
    const t = convexTest(schema);
    const sessionId = await t.run(async ctx => ctx.db.insert('sessions', session.toSnapshot()));
    const client = createClient(t);
    const repository = new ConvexPokerPlanningSessionRepository(client);

    // Act
    const found = await repository.findById(sessionId);
    const snapshot = found?.toSnapshot();

    // Assert
    expect(snapshot?.participants).toHaveLength(1);
    expect(snapshot?.tickets).toHaveLength(1);
  });

  it('should return null when session is missing', async () => {
    // Arrange
    const t = convexTest(schema);
    const client = createClient(t);
    const repository = new ConvexPokerPlanningSessionRepository(client);

    // Act
    const result = await repository.findById('unknown');

    // Assert
    expect(result).toBeNull();
  });
});
