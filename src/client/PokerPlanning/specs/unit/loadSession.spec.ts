import {describe, expect, it} from 'vitest';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadPokerPlanningSession} from '@/src/client/PokerPlanning/application/loadPokerPlanningSession/loadPokerPlanningSession';
import {createDeferred} from "@/src/client/Shared/testing/helpers/createDeferred";
import {PokerPlanningSession} from "@/src/server/PokerPlanning/domain/PokerPlanningSession/PokerPlanningSession";

describe('When loading the poker planning session', () => {
  it('should store participants and tickets', async () => {
    // Arrange
    const session = {
      participants: [{id: '1', name: '<PERSON>', avatar: 'avatar', voted: false}],
      tickets: [{id: 'T1', title: 'Test', status: 'new', votes: 0}],
    };
    const store = createTestingStore({
      pokerPlanningSessionRepository: {
        findById: async () => PokerPlanningSession.fromSnapshot(session),
      }
    });

    // Act
    await store.dispatch(loadPokerPlanningSession('session-1'));

    // Assert
    expect(store.getState().pokerPlanningSession.participants).toHaveLength(1);
    expect(store.getState().pokerPlanningSession.tickets).toHaveLength(1);
  });

  it('should toggle loading state', async () => {
    // Arrange
    const session = {
      participants: [{id: '1', name: 'John', avatar: 'avatar', voted: false}],
      tickets: [{id: 'T1', title: 'Test', status: 'new', votes: 0}],
    };
    const deferred = createDeferred<PokerPlanningSession>();
    const store = createTestingStore({
      pokerPlanningSessionRepository: {
        findById: () => deferred.promise
      }
    });

    // Act
    const dispatchPromise = store.dispatch(loadPokerPlanningSession('session-1'));

    // Assert
    expect(store.getState().pokerPlanningSession.isLoading).toBe(true);
    deferred.resolve(PokerPlanningSession.fromSnapshot(session));
    await dispatchPromise;
    expect(store.getState().pokerPlanningSession.isLoading).toBe(false);
  });

  it('should stop loading if session is missing', async () => {
    // Arrange
    const store = createTestingStore({
      pokerPlanningSessionRepository: {
        findById: async () => null,
      },
    });

    // Act
    await store.dispatch(loadPokerPlanningSession('unknown'));

    // Assert
    expect(store.getState().pokerPlanningSession.isLoading).toBe(false);
  });

  it('should keep participants empty if session is missing', async () => {
    // Arrange
    const store = createTestingStore({
      pokerPlanningSessionRepository: {
        findById: async () => null,
      },
    });

    // Act
    await store.dispatch(loadPokerPlanningSession('unknown'));

    // Assert
    expect(store.getState().pokerPlanningSession.participants).toHaveLength(0);
  });

  it('should keep tickets empty if session is missing', async () => {
    // Arrange
    const store = createTestingStore({
      pokerPlanningSessionRepository: {
        findById: async () => null,
      },
    });

    // Act
    await store.dispatch(loadPokerPlanningSession('unknown'));

    // Assert
    expect(store.getState().pokerPlanningSession.tickets).toHaveLength(0);
  });

  it('should set an error when session is missing', async () => {
    // Arrange
    const store = createTestingStore({
      pokerPlanningSessionRepository: {
        findById: async () => null,
      },
    });

    // Act
    await store.dispatch(loadPokerPlanningSession('unknown'));

    // Assert
    expect(store.getState().pokerPlanningSession.error).toBe('Session not found');
  });
});
