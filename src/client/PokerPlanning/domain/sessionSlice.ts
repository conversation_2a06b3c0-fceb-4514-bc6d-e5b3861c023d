import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {PokerPlanningSession} from '@/src/server/PokerPlanning/domain/PokerPlanningSession/PokerPlanningSession';

export interface PokerPlanningSessionState {
  participants: { id: string; name: string; avatar: string; voted: boolean }[];
  tickets: { id: string; title: string; status: string; votes: number }[];
  isLoading: boolean;
  error: string;
}

const initialState: PokerPlanningSessionState = {
  participants: [],
  tickets: [],
  isLoading: false,
  error: '',
};

const sessionSlice = createSlice({
  name: 'pokerPlanningSession',
  initialState,
  reducers: {
    pokerPlanningSessionLoadingStarted(state) {
      state.isLoading = true;
      state.error = '';
    },
    pokerPlanningSessionLoadingFailed(state) {
      state.isLoading = false;
      state.error = 'Session not found';
    },
    pokerPlanningSessionLoaded(state, action: PayloadAction<PokerPlanningSession>) {
      const snapshot = action.payload.toSnapshot();
      state.participants = snapshot.participants;
      state.tickets = snapshot.tickets;
      state.isLoading = false;
      state.error = '';
    },
  },
});

export const {
  pokerPlanningSessionLoadingStarted,
  pokerPlanningSessionLoadingFailed,
  pokerPlanningSessionLoaded,
} = sessionSlice.actions;
export const pokerPlanningSessionReducer = sessionSlice.reducer;
export const pokerPlanningSessionInitialState = initialState;
