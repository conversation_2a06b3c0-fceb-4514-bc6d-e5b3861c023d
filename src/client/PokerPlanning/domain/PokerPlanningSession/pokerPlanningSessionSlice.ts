import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {PokerPlanningSession} from '@/src/server/PokerPlanning/domain/PokerPlanningSession/PokerPlanningSession';
import {PokerPlanningSessionState} from "@/src/client/PokerPlanning/domain/PokerPlanningSession/PokerPlanningSessionState";

const initialState: PokerPlanningSessionState = {
  participants: [],
  tickets: [],
  isLoading: false,
  error: '',
};

const slice = createSlice({
  name: 'pokerPlanningSession',
  initialState,
  reducers: {
    pokerPlanningSessionLoadingStarted(state) {
      state.isLoading = true;
      state.error = '';
    },
    pokerPlanningSessionLoadingFailed(state) {
      state.isLoading = false;
      state.error = 'Session not found';
    },
    pokerPlanningSessionLoaded(state, action: PayloadAction<PokerPlanningSession>) {
      const snapshot = action.payload.toSnapshot();
      state.participants = snapshot.participants;
      state.tickets = snapshot.tickets;
      state.isLoading = false;
      state.error = '';
    },
  },
});

export const {
  pokerPlanningSessionLoadingStarted,
  pokerPlanningSessionLoadingFailed,
  pokerPlanningSessionLoaded,
} = slice.actions;

export const pokerPlanningSessionReducer = slice.reducer;
export const pokerPlanningSessionInitialState = initialState;
