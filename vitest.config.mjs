import {defineConfig} from 'vitest/config';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
    plugins: [react(), tsconfigPaths()],
    test: {
        env: {
            NEXT_PUBLIC_CONVEX_URL: 'https://test.convex.cloud',
        },
        coverage: {
            provider: 'v8',
            exclude: ['specs/**/*', '**/*.config.*', '**/viewModels/*', '**/domain/*', '**/*Request.ts', '**/*Response.ts', "**/ports/*", '.next/**/*', 'node_modules/**/*', 'coverage/**/*', 'jest.config.js', 'vite.config.js', '.dependency-cruiser.js', '**/*.d.ts'],
        },
        projects: [
            {
                extends: true,
                test: {
                    include: ['**/server/**/*.spec.ts', 'convex/**/*.test.ts'],
                    environment: 'edge-runtime',
                    globals: true,
                    setupFiles: './vitest-setup.mjs',
                    mockReset: true,
                    clearMocks: true,
                    restoreMocks: true,
                    server: {
                        deps: {
                            inline: ["convex-test"]
                        }
                    },
                },
            },
            {
                extends: true,
                test: {
                    include: ['**/client/**/*.spec.tsx', '**/client/**/*.spec.ts', 'app/**/*.spec.tsx'],
                    environment: 'jsdom',
                    globals: true,
                    setupFiles: './vitest-setup.mjs',
                    mockReset: true,
                    clearMocks: true,
                    restoreMocks: true,
                    server: {
                        deps: {
                            inline: ["convex-test"],
                        },
                    },
                },
            },
        ],
    },
});